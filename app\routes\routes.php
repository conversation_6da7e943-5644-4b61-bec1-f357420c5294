<?php

// Define your application routes here

//.env load
$baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';

$router->get('/', function () use ($baseUrl) {
    echo "Welcome to {$baseUrl}";
});

// Default home route - Portal Selection
$router->get('/', 'employee/HomeController@index');
$router->get('/home', 'employee/HomeController@index');

// Employee Portal Routes
$router->get('/employee/login', 'EmployeeController@login');
$router->post('/employee/login', 'EmployeeController@authenticate');
$router->get('/employee/register', 'EmployeeController@register');
$router->post('/employee/register', 'EmployeeController@store');

// PDC Portal Routes
$router->get('/pdc/login', 'Pdc<PERSON>ontroller@login');
$router->post('/pdc/login', 'PdcController@authenticate');

// Admin/HR Portal Routes
$router->get('/admin/login', 'AdminController@login');
$router->post('/admin/login', 'AdminController@authenticate');

// Dashboard Routes (after login)
$router->get('/employee/dashboard', 'EmployeeController@dashboard');
$router->get('/pdc/dashboard', 'PdcController@dashboard');
$router->get('/admin/dashboard', 'AdminController@dashboard');

// Logout Routes
$router->get('/logout', 'AuthController@logout');
