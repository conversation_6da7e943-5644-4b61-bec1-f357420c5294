<?php

// Application bootstrap file
// This file is loaded before the application starts

// Load environment variables
require_once __DIR__ . '/core/env.php';
App\Core\Env::load(__DIR__ . '/../.env');

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('UTC');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define application constants using environment variables
define('APP_ROOT', __DIR__);
define('PUBLIC_ROOT', __DIR__ . '/../public');
define('APP_NAME', App\Core\Env::get('APP_NAME', 'SunnHR'));
define('APP_URL', App\Core\Env::get('APP_URL', 'http://localhost/sunnhr'));
define('BASE_URL', APP_URL);

// Load .env + constants
// require_once __DIR__ . '/config/config.php';
// loadConfig();

// Auto-load all helper files
foreach (glob(__DIR__ . '/helpers/*.php') as $file) {
    require_once $file;
}