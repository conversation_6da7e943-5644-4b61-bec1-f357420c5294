<?php

/**
 * Helper function to get environment variables
 * 
 * @param string $key The environment variable key
 * @param mixed $default Default value if key doesn't exist
 * @return mixed
 */
function env($key, $default = null)
{
    return App\Core\Env::get($key, $default);
}

/**
 * Get application name
 */
function app_name()
{
    return env('APP_NAME', 'SunnHR');
}

/**
 * Get application URL
 */
function app_url()
{
    return env('APP_URL', 'http://localhost/sunnhr');
}
